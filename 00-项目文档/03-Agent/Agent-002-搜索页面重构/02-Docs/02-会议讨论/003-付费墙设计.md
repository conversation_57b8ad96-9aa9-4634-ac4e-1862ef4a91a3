
---

不不不 我不认可 

没有卡缝，没有交接带

只有上一张卡片和下一张卡片

未到限制下一张卡片完整内容 

一到限制，下一张卡片付费墙

结合你所说的容量证明

O3 

明白了。我们就按你坚持的“原子化卡片”模型来定：界面上始终只有上一张卡片与下一张卡片两者的切换；在未触发限制时，下一张是完整内容卡；一旦触发限制，下一张就不再是内容卡，而是付费墙卡（一种特殊类型的卡片），并在这张卡上呈现“容量证明”。没有“卡缝”与“过渡带”的概念，只有“下一张卡片位由谁来占据”的切换。

行为与时序的准确描述

从搜索或上一张内容卡上滑，下一张卡片随手势进入窗口。当天剩余探索次数>0 时，达到你既有的“替换阈值”后，下一张完整内容卡覆盖上一张，交互维持现状；当天剩余次数=0 时，达到同一个“替换阈值”的那一刻，下一张卡片位由“付费墙卡”接管，以完整卡片的方式覆盖上一张。也就是说，用户到限制的那次上提，看到的不是“下一词条的正文”，而是“付费墙卡”的完整版式；这张卡与普通内容卡拥有同等的层级、同样的进入/退出动效，只是卡内组件不同。

付费墙卡的版式与“容量证明”

付费墙卡的抬头区域显示**“继续探索 · {目标词}”的标题，紧随一行超短预览**（仅 1 行、≤40 字，非正文，不可展开），用于确认方向感；主体区域用容量证明替代正文：例如“可立即解锁：12 条高质量例句、8 个常见搭配、6 个关联跳转、离线浏览”。容量证明的数据在上一张卡滚动时提前预取，只取计数与标签，不拉取正文，保证“证据可见、内容不泄露”。操作区只给出三条明确分岔：立即解锁（走内购，成功后“付费墙卡”原地无缝切换为完整内容卡）、明日再来（说明次日自动恢复 10 次，点后返回上一张内容卡的底部）、继续阅读当前卡（把视口平滑回到上一张卡的可读位置）。全程不降级、不错位、不遮挡用户已经获得的内容。

动效、手势与触感的对齐

为了让肌肉记忆不被打破，阈值不变：仍是你现有的“替换阈值”。区别只在“达到阈值之后由谁来覆盖”。当日有额度时，覆盖对象是“下一张内容卡”；当日无额度时，覆盖对象是“付费墙卡”。建议在首次触发付费墙卡时给一次轻触感反馈，并暂停上一张卡底部的“向上箭头 + 文案”动画，强化“已到边界”的心智。若用户手指回拉到阈值以下，付费墙卡按与你现在内容卡一致的回弹规则退出，视口回到上一张卡底部；再次上推，仍然由付费墙卡覆盖。

网络与离线的容错

容量证明只依赖计数与简要标签，可在弱网下优先返回计数，再懒加载标签文本；网络完全不可用时，付费墙卡以“需要联网完成解锁；你可继续阅读当前卡”的行内提示降级，按钮置灰但提供“返回上一张卡”的立即操作。任何情况下，上一张卡的内容从不被遮盖或降级。

可达性与文字规范

付费墙卡进入时，为 VoiceOver 读作：“继续探索：{目标词}（预览）——解锁后可见：例句 {N}、搭配 {M}、关联 {K}、离线浏览——按钮：立即解锁 / 明日再来 / 返回当前卡。”动态字重下卡高自适应，但不超过一个屏高的 85%，底部留出安全区，避免与系统手势冲突。文案保持“邀请而非惩罚”的基调，强调“为下一步付费”，而非“拿回已得的东西”。

事件与埋点（便于评估方案 B 的纯净边界）

保持你既有的“上滑替换”事件链，只新增与复用少量事件名即可：
	•	card_next_prefetch_meta（预取下一词的计数与标签名）；
	•	paywall_card_impression（付费墙卡进入视口）；
	•	paywall_card_action_unlock / later / back_to_current;
	•	paywall_to_content_seamless_switch（解锁成功、无缝切换完成）。
核心观测指标聚焦三点：遇墙→解锁转化率、遇墙后仍停留时长、次日回访率。若首购证据不足，可在“容量证明”中增加占位条（如 12 个例句的 3 个灰色占位块图标），但仍不展示正文，维持“边界纯净”。

一段贴近你实现的用户故事

刘小姐从“single point of failure”上滑进入下一张。当天还剩 1 次，她顺利进入“graceful degradation”的完整内容卡。再上滑，额度已用尽；她手势跨过同一个替换阈值，覆盖出现的不是“redundancy”的正文，而是一张付费墙卡：抬头是“继续探索 · redundancy”，下一行短预览“通过并联冗余避免 SPOF…”。中部写着“解锁后即刻可见：12 条高质量例句 · 8 个常见搭配 · 6 个可继续的关联 · 离线浏览”。她点“明日再来”，卡片优雅退出，视口回到上一张内容卡的底部；第二天次数恢复，再上滑，同一位置自然换上“redundancy”的完整内容卡。如果她在当天选择“立即解锁”，系统完成内购后，付费墙卡不离位，而是无缝切换为“redundancy”的完整正文，阅读流没有断点。

—

这样实现，既严格遵守你“只有上下两张卡、无卡缝”的原子化视觉，也把方案 B 的“纯净边界 + 容量证明”落成同一阈值、同一动效、同一层级的替换逻辑。若需要，我可以把“付费墙卡”的组件明细（标题区、预览行、容量证明、操作区）的字数与排版限值、内购失败重试规则，以及对应的伪代码状态图整理成规格段落，直接补进《需求文档整合》的“付费墙”章节。
---

我认为修改成

付费墙卡只有二个选择 升级到 premium 和 返回搜索

减少多选择带来的犹豫和流失

返回搜索直接带来对探索进度丢失的损失厌恶（我想继续看下去 而不是重新开始）

O3

你要的“只有两个选择：升级到 Premium / 返回搜索”的付费墙卡，在你的原子化卡片模型里是自洽的。它把“继续探索”的决策变成一次明确的二分：要么立刻进入无限流，要么回到强意图的搜索起点。减少分叉确实能降低犹豫时间，也更容易塑造“向前一步属于订阅权益”的长期心智。关键是在剥离“明日再来/继续当前卡”这些“第三条路”的同时，保证不产生被惩罚感，并把“容量证明”做得足够具体，让用户知道自己为哪些确定的东西付费。

呈现与时序。 到达当日上限时，下一张卡片不再是内容卡，而是以“完整卡”的形式切入的付费墙卡：抬头固定为「继续探索 · {目标词}」，下一行是一句不展开的极短预览，仅用于确认方向。正文区域不展示任何该词的深度内容，直接给出容量证明，比如“立即解锁可见：12 条高质量例句 · 8 个常见搭配 · 6 个可继续的关联 · 支持离线浏览”。底部只保留两个主按钮：升级到 Premium（主色、右侧）与返回搜索（次要、左侧）。阈值、进入/退出动效与现有内容卡完全一致——唯一改变的是“阈值之后由谁来覆盖”。这样用户的肌肉记忆毫无变化，但被明确告知“下一步属于订阅”。

用户故事。 刘小姐从 graceful degradation 上滑，额度已用尽。她越过同一个替换阈值，屏幕覆盖出现一张新卡：标题「继续探索 · redundancy」，下一行“通过并联冗余避免 SPOF…”。中段不是正文，而是一行行坚实的容量证明。她一眼就明白：只要现在解锁，下一秒就会看到这 12 条例句、8 个搭配、6 个关联跳转，并且之后不再受次数限制。她犹豫片刻，选择了返回搜索；页面以与你现有“返回”的动效回到搜索框，原查询自动回填，顶部出现一条细提示“今日探索 10/10 · 明日 0:00 自动恢复”。第二天她再次上滑到同一位置，这一次看到的就是真正的 redundancy 全文卡了。若她当天在付费墙卡里选择升级到 Premium并完成内购，付费墙卡不离位，直接无缝切换成 redundancy 的完整内容卡，阅读流被自然接上。

为什么去掉第三选项不会伤害体验。 你的产品设定里，“查词＝无限制”“浏览＝受限”。返回搜索并不会让用户“回不去”，它只是把用户带回工具型的强意图路径；真正被限制的是“继续刷下一张”。这与“拿走当前卡内容”有本质区别，因此不会触发“被收回价值”的强烈逆反。同时，二分法显著缩短了决策时间，也避免了“我先点明天再来吧”的逃避式选择；“返回搜索”的细提示（明天自动恢复）则把“损失厌恶”留作一种温和但持续的心理张力。

几条落地细节，保证决策干净但不粗暴。 第一，付费墙卡仍应在抬头保留“标题 + 一行预览”，但绝不露正文；容量证明的数据提前预取，只取计数与标签，不取内容，确保证据可见、价值不外泄。第二，返回搜索后自动聚焦搜索框，并保留原查询，顶部以 2–3 秒的细提示说明“今日用尽/明日恢复”，既完成教育又不打扰。第三，系统手势（边缘返回）正常工作，但落点与按钮一致：从付费墙卡边缘返回就回到搜索，而不是回到上一张内容卡，避免建立“第三条隐形退路”。第四，弱网/离线时，按钮仍只两项；升级按钮在卡内联显示进度与失败重试，不改变“返回搜索”的即时可达性。

指标与权衡。 两按钮会提高“遇墙→付费/离开”的确定性，但也可能提升短期跳出。你的优势是“查词永远可用”，所以“返回搜索”的跳出并不等于会话结束；建议以“遇墙→解锁转化率”“遇墙后仍停留或回到搜索的比例”“次日回访率”三者共同评估。如果担心过于刚性，可以只对D0–D1 新用户上线二分法，对老用户保留第三项“继续当前卡”（不返回搜索），跑一轮 A/B 看留存曲线是否受影响，再决定是否全面收敛到二分。

文案建议（可直接用）。
标题：继续探索 · {redundancy}
预览：通过并联冗余避免单点故障…
容量证明：解锁后立即可见：12 条高质量例句 · 8 个常见搭配 · 6 个可继续的关联 · 支持离线浏览
主按钮：升级到 Premium（¥X/月，细字链接：“使用相同商店账号可恢复购买”）
次按钮：返回搜索（细提示：“今日 10/10 · 明日 0:00 自动恢复”）

用这套实现，你既保住了原子化卡片的节奏与质感，又把付费决策简化为清晰的二选一，并通过容量证明把“我现在付费会立刻得到什么”说透。需要的话，我可以把这张付费墙卡的组件规格（字数上限、占位与对齐、动效与触感时序、失败重试与无障碍规则）整理成一页可直接交付给设计与前端的规格稿。

---
付费墙有三种套餐 月度订阅，年度订阅，终生 设计付费墙的完整信息架构

你已经把“卡片付费墙 = 二选一（升级 / 返回搜索）”定为主框架，三种套餐只是“升级路径里的选择”。因此信息架构要做到两个目标：在付费墙卡上让用户一眼确认价值并直接下单（默认推荐一个方案），而把“改套餐”变成一个可选的次级动作，以免把二分决策重新复杂化。下面按“从看到付费墙到完成购买”的顺序，给出一套可直接落地的架构与文案规范。

一、付费墙卡（卡片层，二选一决策，不暴露正文）

这张卡与内容卡同级进入，仅替换“下一张内容卡”的位置。它只承担三个信息职责：确认方向、证明容量、给出一个默认已选的购买选项。

信息块顺序（自上而下）：
1）抬头（方向确认）：继续探索 · {redundancy}；下一行仅 1 行预览（≤40 字）确认“前方是什么”，不可展开。
2）容量证明（证据而非正文）：以数据条目呈现“解锁后立刻可见”：例句 12 · 搭配 8 · 可继续的关联 6 · 支持离线浏览。
3）价格锚点（默认方案）：一行强调**“年度订阅更划算”**的等效价：年度订阅 ¥X/年（≈¥Y/月，省 Z%）。
4）主操作区（仅两项）：
— 按钮 A：升级到 Premium（主色，文案包含默认方案，如“立即以 ¥X/年 解锁”）；
— 按钮 B：返回搜索（次要，返回后在搜索页顶部短提示“今日 10/10 · 明日 0:00 自动恢复”）。
5）次级微链接（不打扰）：更改方案（点开后才出现三个套餐的选择器）；恢复购买；细字法务说明一句（自动续订与管理入口）。这些都不应与主按钮同层级视觉竞争。

文案示例：
标题：继续探索 · redundancy
预览：通过并联冗余避免单点故障…
容量：解锁后立即可见：12 条高质量例句 · 8 个常见搭配 · 6 个可继续的关联 · 支持离线浏览
主按钮：立即以 ¥128/年 解锁（示例）
次按钮：返回搜索
细字：自动续订，可在账户设置取消 · 使用相同商店账号可恢复购买 ｜ 更改方案 ｜ 恢复购买

二、升级路径（用户点“升级”后出现的最小化选择器）

你的原则是“不要让用户犹豫”。因此，默认选中“年度订阅”并直接下单；只有当用户点了“更改方案”，才展开一个紧凑选择器（可以是底部弹层或在卡内原位展开），供用户在月度 / 年度 / 终生三者中切换。切换时，主按钮文案与价格即时联动。

选择器的信息结构：
	•	三方案同权卡片（不做功能差异，只做计费差异）：
	•	月度订阅：¥m/月（自动续订）
	•	年度订阅（推荐）：¥a/年（≈¥a/12 每月，省 Z%）
	•	终生：¥l 一次性（非订阅）
	•	一条说明（细字）：订阅类自动续订；试用/优惠（若有）规则；终生为一次性购买；管理订阅深链与恢复购买。
	•	确认区：主按钮“以 {当前选中方案价格} 立即解锁”；取消返回上一层。

交互要点：默认不展开选择器；展开后不增加第三个主按钮，仍然只有一个“购买”动作，方案切换只是改变这个按钮的价格与条目，保持决策“单点完成”。

三、三种套餐的对比呈现（只强调“价格/承诺期”，不制造“功能落差”）

所有功能权益在三个方案间完全一致，否则会制造新的犹豫。对比只围绕价格、付费节奏与适配人群展开，让用户用“时间跨度与投入强度”的视角决策。

方案	计费	展示主张	适用人群	默认态
月度	自动续订	灵活按月用	先试后定的轻度使用者	备选
年度（推荐）	自动续订	折算更低、稳定可用	每日/每周常用者	默认选中
终生	一次性	长期投入、免续订	重度学习与忠诚用户	备选（保留但不高亮）

文案规则： 年度方案一律显示等效月价 + 节省百分比（相较月度）；终生方案文案突出“免续订/一次购入”，避免与订阅混淆。

四、法务与平台约束的最小披露（不打断）

在付费墙卡底部保持一行细字，满足上架要求即可：
	•	“订阅将自动续订，除非在当前周期结束前 24 小时取消。可在账户设置管理或取消订阅。价格因地区而异。”
	•	链接：隐私政策 / 用户协议 / 订阅条款 / 恢复购买。
	•	终生为非消耗型购买，支持恢复；（如支持）提示“家庭共享按商店规则”。

五、状态与异常的呈现（不破坏二分结构）

支付进行中用卡内联进度覆盖主按钮；成功后不离位、直接把付费墙卡无缝切换成目标“内容卡”；失败用一行红色轻提示 +重试，保留“返回搜索”。弱网/离线时，主按钮置灰并给“需要联网完成解锁”的行内说明；容量证明依旧可见。

六、返回搜索的一致性

“返回搜索”始终把用户带回搜索页，自动聚焦搜索框并回填上一次查询，在顶部短提示“今日 10/10 · 明日 0:00 自动恢复”。边缘返回手势与左上角返回按钮的落点与该按钮保持一致，避免隐形第三路。

七、埋点与评估（验证“二选一 + 年度默认”的效果）

在不增加界面复杂度的前提下，追踪四类核心事件：paywall_impression（付费墙卡曝光）、upgrade_click（主按钮点击）、plan_switch（用户主动展开并切换方案）、purchase_success/fail、back_to_search。关键指标看三条链路：遇墙→升级点击率、遇墙后仍停留/回到搜索的比例、次日回访率。若年度默认导致短期转化高但退款率升，考虑在选择器里为年度增加“7 天试用/折扣价”（平台允许时），仍不改变付费墙卡的二分结构。

⸻

这套信息架构保证了你的两条底线：一是决策保持二分，升级/返回搜索清晰无歧义；二是三套餐被“收纳”在升级路径里，默认“年度订阅”直接购买，只有刻意展开才换方案。用“容量证明 + 年度等效价 + 节省百分比”把价值说透，用无缝切换把购买后的阅读流接上。若你愿意，我可以把文案字数上限（各块 14/24/40 字）、按钮层级、进度与错误态，以及选择器的最小尺寸规范整理成一页规格，直接并入你的《需求文档整合》。

---
除了容量证明 参考用户故事 我认为是不是还需要权益列表之类的信息架构 

可以加，但不要把“权益列表”做成另一个决策面板。你的付费墙卡已经是二选一（升级 / 返回搜索），因此“权益”只承担解释价值的职责，而不是制造新分叉。更准确的做法，是把“容量证明”作为即时证据，“权益列表”作为长期承诺，两者放在同一张付费墙卡里、同一层级完成说明。

放在哪、展示到什么程度

在卡片结构里，顺序应当是：标题与一行预览 → 容量证明（本词当下能看到多少）→ 精炼的权益列表（长期、跨词生效） → 两个按钮。权益列表只展示三到四条最高区分度的能力，用“动词 + 对象”的句式，例如“无限跳转相关概念”“解锁高级例句与搭配”“离线大词库”“跨语对照/TTS”。这几条应当同权于三种计费方案（月/年/终生内容一致，仅计费方式不同），避免在付费墙上再引入“哪个方案功能更多”的犹疑。若还有次要权益（无广告、历史足迹与练习簇、优先下载通道等），放在“展开更多”里，以披露为目的，不改变“二选一”的决策简单度。

为什么需要“权益列表”

容量证明解决“现在就能看到什么”，权益列表解决“从此以后都具备什么”。前者是即时动机，后者是承诺与安全感。两块信息合在一屏，用户可以在同一次注视中完成价值判断，而不必跳出去阅读说明或比较页面。

一张对齐内部的对照表（可放入规格）

维度	容量证明（即时）	权益列表（长期）
目的	证明“解锁后这张卡立刻能看到的量”	说明“升级后在所有卡/所有天可用的能力”
形式	纯数据：例句 N、搭配 M、可继续关联 K、离线可用	3–4 条动宾短句：无限跳转 / 高级例句与搭配 / 离线大包 / 跨语对照与TTS
作用范围	当前目标词	全局（跨词、跨会话）
交互	不可展开	可“展开更多”，但不新增按钮
与套餐关系	无差异	无差异（同权不同价）

文案与密度建议

在你的段落化风格里，每条权益不超过 16–18 个字，避免技术术语堆叠；容量证明保持数字醒目、名词克制。一个可直接落地的组合如下：
	•	容量证明：解锁后立即可见：12 条高质量例句 · 8 个常见搭配 · 6 个可继续关联 · 支持离线浏览。
	•	权益列表（四条以内）：无限次相关跳转；高级例句与搭配库；离线大词库；跨语对照与 TTS。
	•	其余通过“展开更多”披露：无广告；历史足迹与主题练习簇；优先下载与差量更新。展开不改变按钮区，仍然只保留“升级到 Premium / 返回搜索”。

触达位置与一致性

这些权益措辞要在整个产品里保持一致：在浏览模式的灰锁按钮上写同一语句，在语言包管理页与“⋯更多”的订阅说明里复用同一短句。这样用户在不同入口看到的是同一个“能力名”，不会因为场景变化产生理解偏差。

动效与反馈

当用户从付费墙卡“升级”成功，不离位、直接把“容量证明 + 权益列表”区域淡出，替换为完整内容卡。同时在卡尾部弱提示一次“已解锁：无限跳转 / 离线大词库”，用同样的权益措辞做一次“兑现提醒”，然后不再反复打扰。

⸻

总结一下：要加权益列表，但把它定位为“承诺说明”，而不是“第三种选择”。在一张卡里，容量证明给“当下证据”，权益列表给“长期承诺”，两者合力支撑主按钮的决策，而不破坏你坚持的二分结构。如果你同意，我可以把这部分整理成可交付的付费墙卡规格（字段顺序、字数上限、展开规则、动效与可访问性），直接补到文档中。

---
部分paywall 会有视觉要素，比如插画，徽章来表达权益，premium 的地位，你认为我应该怎么做 但是好像空间不足了

你已经把“付费墙卡=二选一（升级 / 返回搜索）+ 容量证明 + 精炼权益列表”定住了，视觉元素就要服务于加速理解与确立等级，而不是喧宾夺主。在空间紧张的卡片里，插画/徽章要转为微图形（micro-visuals）与层级化排版：用很小的、信息承载强的符号来完成“Premium 的地位感”“权益可视化”“品牌气质”三件事，同时保证主信息（标题、容量、价格与按钮）永远在首屏。

将“插画”收缩为徽章与微图形。 不放大幅 Hero 图，改用 20–24pt 的“Premium 徽章”贴在标题行右侧（或标题行上方做 14–16pt 的细标“Premium”胶囊），只出现一次、弱渐变或镭射纹理点到为止。它的任务是“宣布层级”，而不是占据注意力。真正承载价值的是“容量证明”的数字和“权益列表”的动宾短语。

把权益图像化，但不让图像变成主角。 每条权益配一个线性图标（24pt），置于文字左侧，整行高度控制在 40–44pt，最多三至四条；超出改为“展开更多”。图标的作用是扫读锚点，不是“画面感”。图标风格保持与系统 SF Symbols 一致（或等权重的线框），避免彩色填充抢走注意。这样既有“视觉要素”，又不扩大版面。

在“容量证明”里使用数字芯片，取代插画。 例句/搭配/关联/离线四项用紧凑的“数字芯片”表示：12 例句、8 搭配、6 关联、离线，每个芯片含 16pt 小图标 + 数字 + 1 个词，水平排两行即可。数字远比插画更能建立“现在就有货”的确定性，也最节省空间。

CTA 是视觉重心，徽章辅助不喧哗。 主按钮“升级到 Premium”可在按钮左侧内嵌 16pt 小皇冠/星徽，或在按钮上方加一条 12pt 的“年度更划算（≈¥Y/月，省 Z%）”行作为价格锚点。按钮本身做层级，徽章只是“点睛”，避免按钮外还有第二重“去点我”的大型视觉。

背景只做极轻的层次分割。 顶部 15–20% 的浅色渐变或柔和纹理（仅在高屏可见），把标题区与内容区分层；在小屏或动态字重放大时自动去除背景装饰，优先保留文字与按钮。不要在卡片内再放独立插画区——这会挤压首屏的容量与权益。

两个密度模板，自动切换。
以屏幕有效高度设两档模板：紧凑版优先展示“标题+预览 → 容量芯片 → 三条权益 → 两按钮”，宽裕版再允许在标题左侧加入 24pt 的徽章或在卡底加入一条品牌插画“水印带”。触发布局切换的阈值可以用安全区后的可用高度（如 < 640pt 用紧凑版）。

区块	紧凑版（默认）	宽裕版（高屏）
标题行	文本 + 16pt Premium 胶囊	文本 + 20–24pt 徽章
容量证明	两行数字芯片（最多 4 个）	同左
权益列表	3 条（24pt 图标 + 14/16pt 文本）	4 条（其余“展开更多”）
背景	纯色	顶部极浅渐变/纹理
CTA	两按钮	两按钮（可加等效月价细行）

版式与尺寸基准，确保不挤爆。 用 8pt 栅格：主内边距 16pt，区块间距 12–16pt，图标 20–24pt，数字芯片高度 28–32pt，按钮高度 48pt。这样一屏里，标题区 ~64–80pt、容量区 ~64pt、权益区 ~132–176pt、按钮区 ~56–72pt，加上内边距，刚好落在大多数机型的首屏安全范围内。

动效只在首曝轻点一下。 Premium 徽章进场 200–250ms 淡入+微缩放一次即可；按钮按下回弹 120ms；不要做循环发光或骨动，避免与“学习工具”的气质冲突。徽章动效仅首曝触发，二次出现静态，防止打扰。

颜色与对比保持“功能第一”。 Premium 主色只用于按钮与徽章，不进入正文；容量芯片使用中性色底+深文字，利于快速比较。对暗黑模式要有等价款（尤其是渐变/水印需替换为对比安全的版本）。

可访问性优先。 徽章与图标必须有文本等价，不要在 VoiceOver 里读“装饰”；按钮的可触区域≥44×44pt；在动态字重 Large/XL 下自动隐藏插画背景与次要徽章，保留标题、数字与按钮。

最后用指标检验“加的值不值”。 你可以 A/B 三档视觉强度：A=无插画只徽章；B=徽章+数字芯片（推荐基线）；C=在高屏加水印带。看三项：遇墙→点击升级率、决策时间（impression→点击的中位时长）、返回搜索率。若 C 提高点击却拉长决策时间，说明插画正在分散注意；此时回到 B。

把“插画的叙事力”压缩为徽章+数字+线性图标，就既传达了 Premium 的地位，也把空间留给“为什么现在解锁”的关键信息。若你同意，我可以把这张付费墙卡的视觉规格写成一页：包括栅格、字号、图标线宽、色值与暗黑模式替换表，以及在小屏/大字重下的自动裁剪规则，方便设计与前端直接对齐。

