那否决本地机器翻译路线，因为不可用

我们还是回到搜索页面和付费墙的问题

搜索页面 - 出现搜索建议 - 点击搜索建议跳转内容卡片 

第一个问题：内容卡片你认为是从底部弹出（模态） 顶部底层仍然是搜索，上滑触发下一个单词卡片，模态不变，新卡片覆盖旧卡片，还是右侧划入（全屏） 

---

O3

我更倾向于把“内容卡片”作为**全屏页面（右侧推入）**来承载，把“底部弹出的模态”仅当作一次性“窥视（peek）”的补充工具，而不是主承载。原因不是审美取向，而是由你的叙事与约束共同决定：搜索是强意图、卡片是深阅读、相关浏览是长路径、付费墙要“原位解锁且不遮挡当前卡”。这四件事叠在一起，意味着你需要一个能承受长滚动、可多跳转、可深链接、手势稳定且不堆叠模态的容器，典型解法就是导航控制器的全屏路由。

在信息表达上，全屏路由的优势在于可以把“当前卡片”视作一条纵向的叙事轴，把“下一张相关卡片”以卡缝预览的方式露出 10–15% 的首屏，用户上滑即可无缝进入下一张；当遇到配额边界时，你在“卡缝”位置插入轻量的原位解锁面板，既不遮住当前卡，也让“前方是什么”清晰可见。这种“缝上拦、身后不动”的形态，和你之前确立的付费墙原则天然一致。与此同时，全屏路由保留了系统级“边缘返回”手势与 URL 深链接能力，便于分享与回访；而且不会出现“模态叠模态”的滚动冲突与可达性问题。

底部模态看似更“就地”，但当它成为主承载时，问题会连锁出现。首先，用户一旦进入“相关内容的无限浏览”，你需要在同一层级持续堆叠内容，而模态本质是短暂与打断式的容器：它要么遮蔽下面的搜索页并与之同时滚动产生冲突，要么被用户上滑到“全屏 detent”后变成伪全屏，进而丧失作为“轻层”的意义。其次，你的付费墙需要在“下一张卡片应出现的位置”原位展现；若承载是模态，层级实际上是“搜索页（底层）—模态（中层）—付费面板（顶层）”，三层叠加非常容易让手势、焦点与可达性变得复杂。再者，模态难以优雅承载“多跳回溯”的导航史与外部深链接，用户从推送或分享回到应用时，不易在同一状态重现“我刚才看到的第 N 张卡片”。

为了让取舍直观，下面给一张对照。它不是要点式罗列，而是把两种容器在同一条叙事链上比较“代价”。

容器选择	搜索→首卡的过渡	多卡连续浏览	付费墙原位解锁	返回与深链接	可达性与手势一致性	结论
底部模态为主	从底层搜索到上层卡片，视觉邻近好，但层级变多	模态全屏化后变“伪页面”，滚动与手势易冲突	需三层叠加，顶层面板易遮挡上下文	弱；模态难承载稳定的路由与复现	易出现嵌套滚动与焦点问题	不宜做主承载，仅作“短暂预览”
全屏右侧推入	符合 iOS 的“从查询到详情”的系统心智	纵向轴天然承载无限流与卡缝预览	在卡缝插面板，阻前不阻后，叙事连续	强；可 URL 化，也有系统返回手势	简洁，辅助技术处理更稳	建议作为主承载

这并不排斥你保留“底部模态”的价值。我的建议是采用一种混合峰值：当用户在搜索建议里长按或轻点信息图标时，用底部模态以 60–70% 高度给出“快速预览”（发音、最短释义、2 条例句）；用户若上滑到最大 detent 或点击“查看全部”，就将该模态无缝提升为全屏路由（iOS 现在支持从 sheet 提升为 full screen 的连贯动效）。这样你既有“就地窥视”的敏捷，又把“认真阅读与连跳浏览”托管到更稳健的容器上。

从交互细节出发，我建议把“搜索→卡片”的转场设计成一种带锚点的推入：卡片顶部的“词形+发音+核心义项”作为“英雄元素”，从搜索建议中的同名条目“飞入”卡头，形成视觉连续；首次进入时，在卡底露出下一张相关卡片的标题与一行预览，建立“这是可以继续滑的”心智。到达配额时，在两卡之间的缝隙插入解锁面板，面板顶部保留“下一卡标题 + 一行预览”，主按钮“继续探索（解锁）”，次选项“明日再来/继续当前卡”。关闭后，当前卡保持完整，历史卡可随时回看；这一切都依赖全屏路由的单层滚动，避免了模态叠加的交互债。

最后说两个工程与指标层面的落点，确保选择全屏路由不仅“体验对”，也“可测量、可优化”。其一，预取与骨架屏：在用户指尖停留于某条搜索建议 ≥120ms 时，后台预取首卡与第一张相关卡片，进入页面后先用骨架占位，再平滑回填；这能把“点击—首义项可读”的时间压到 200ms 内。其二，心流指标：以“首卡可读时间”“首跳发生率”“遇墙到解锁转化率”“遇墙后仍停留时长”作为核心度量，对比“底部模态主承载 vs. 全屏路由主承载”的 A/B，通常你会看到后者在“首跳发生率”和“遇墙后仍停留”上更稳，因为它减少了层级切换带来的轻微疲劳。

综上，主承载选全屏右侧推入，保证搜索—阅读—浏览—解锁是一条不中断的叙事链；底部模态只做预览，承担“看一眼就走”的低成本任务，并可一键提升为全屏。这样既保留了极简与速度感，又为你的“原位解锁、不遮挡当前卡”的付费墙策略提供了最稳的舞台。若你愿意，我可以把这套转场的动效时序、卡缝露出高度、预取阈值和无障碍（VoiceOver/动态字重）细则整理成一页规格，直接放进你的需求文档。


