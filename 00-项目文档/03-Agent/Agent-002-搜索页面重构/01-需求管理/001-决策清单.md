* 决策点：**搜索即首页与单根路由**

  * 结论：将“搜索”设为应用的根路由与默认回点；一切从搜索开始、也回到搜索。
  * 分析和理由：把强意图入口放在首屏，用户从“找入口→找答案”的两段式路径压缩为一步，减少往返与犹豫；返回语义统一为“回到搜索”，避免“回上一个卡还是回主页”的歧义，提升可预期性与可撤销性。首页因此成为系统状态的“枢纽”，任何与下一次搜索直接相关的信息（配额、语言、下载、身份、历史、建议）都能聚合于此，形成稳定的心智模型。逻辑的顺畅来自于意图、路由、状态三者的同位：意图明确让流程短，单根路由让导航轻，状态聚合让信息相关且不分散。

* 决策点：**状态单元的数量上限与优先级**

  * 结论：任何时刻最多同时展示 3 个状态单元；当候选≥4 时，按“下载/同步进度 ＞ 当日配额 ＞ 语言/地区 ＞ 身份/会员”裁剪可见项。
  * 分析和理由：首页的主角是“输入与建议”，状态越多越打架。上限保证读屏稳定、视线跳转少；优先级按对“是否能继续使用”的即时影响排序：进度直接决定资源是否就绪，其次是还能不能继续浏览，再是看得懂与否，最后才是长期身份标签。这样用户第一眼总能看到“此刻最关键”的事情。

* 决策点：**输入聚焦时的统一收敛**
  * 结论：输入框聚焦（或主任务占用）时，所有搜索框附近的状态，按钮，文字等消失，将更多竖向空间和注意力让给搜索建议列表。
  * 分析和理由：在“我要输入”的瞬间，系统应降低噪声、让出舞台；统一收敛避免各模块各自“抖动”的混乱，保持节奏一致。避免复杂状态切换带来样式复杂性。

* 决策点：**历史与建议并列，建议在聚焦时接管**

  * 结论：未聚焦时展示最近搜索，聚焦后由建议列表接管主舞台 
  * 分析和理由：历史承接“继续上次”，聚焦后优先建议，减少来回输入成本、提升命中率；

* 决策点：**内容卡的导航与返回一致性**

  * 结论：内容卡为全屏、系统式推入/弹出；边缘返回或左上角返回按钮始终回到搜索，回到搜索时不聚焦，正常显示所有状态和提示组件
  * 分析和理由：遵循平台心智减少学习成本；统一返回目标消除路径岔义；

* 决策点：**浏览配额边界（工具不设限、浏览设日配额）**

  * 结论：查词不受限；无限上滑内容探索，纳入“每日 N 次”配额（默认 10/日，本地时区午夜重置），每次成功替换新单词之后，顶部下弹剩余次数（对于非 premium 用户）
  * 分析和理由：让“工具价值”先被完整体验，信任与留存优先；把“浏览心流”设为订阅权益，建立清晰的边界与转化动机。日配额天然易懂、可预期，不会像会话配额那样令人困惑或焦虑。

* 决策点：**付费墙的触发与载体（原位替换下一张卡）**

  * 结论：当剩余配额为 0、用户上滑到阈值时，“下一张内容卡”被“付费墙卡”原位替换，动效与卡片行为保持一致。
  * 分析和理由：保留“原子化卡片”的双片节奏：永远只有上一张与下一张。遇墙不“盖住”当前内容、不“收回”已给价值，仅“前方这张是谁”被替换，既尊重已得体验，也把付费请求置于价值峰值位置，最大化心甘情愿的转化。

* 决策点：**付费墙的主决策路径**

  * 结论：付费墙只提供两条主路径：升级到 Premium、返回搜索；不提供更多分支。
  * 分析和理由：二分闭环减少犹豫点，避免“跳出阅读去比价/浏览说明”的流失。返回搜索带来“进度记录损失厌恶”的温和牵引（我刚才探索的记录我不想重新开始），但不胁迫——用户感到掌控、而非被驱赶。

* 决策点：**付费墙的内容结构（容量证明 + 权益列表）**

  * 结论：抬头为“继续探索 · 目标项”，下一行仅 1 行 Core definition ；正文由“容量证明（立即可见的数量证据）+ 精炼权益（≤4 条长期能力）”组成，不泄露下一卡正文。
  * 分析和理由：容量证明铺陈“此刻就能多看到什么”，把付费的即时收益具体化；权益列表承诺“从此以后都具备什么”，形成长期价值心智。禁止预先泄露下一卡内容，避免“偷看后离开”的反激励，同时维持对“解锁”的期待与尊重。

* 决策点：**升级后的衔接与失败处理**

  * 结论：升级成功后原地无缝切换为目标内容卡；失败或弱网在卡内联提示并可重试，“返回搜索”始终可达。
  * 分析和理由：成功路径必须零缝隙地续上心流；失败不把用户“赶回”或迫使跳转，降低挫败感，确保随时有退路，减少流失。

* 决策点：**从付费墙返回搜索的“边界回声”**

  * 结论：返回搜索后顶部短提示 2–3 秒，告知“探索额度今日用尽、明日重置”，随后自动淡出。
  * 分析和理由：形成清晰的心理锚点，补齐“为什么被拦”的解释而不打扰当前操作；用可预测的时间点（本地午夜）降低焦虑与无措。

* 决策点：**语言/解释语言选择与资源下载**

  * 结论：
    - 单词索引（单词+coredefinition 用于提供搜索建议）
    - 语言状态常驻首页可见；作为第三状态（前面分别是会员状态，探索额度）使用药丸胶囊+进度圆环设计
    - 用户初次进入时进度，监测到WiFi环境会自动下载
    - 用户点击胶囊，底部弹出 presentation sheet 语言设置组件
      - 允许下载（switch 默认 on ）
      - WiFi环境自动下载（switch 默认 on）
      - 学习语言（list 默认 English）
      - 讲解语言（list 默认 简体中文）
        - 点击后覆盖新 sheet 包含所有支持 sl 
        - 配置跟随App版本更新（写入Target而非 KV Config）
      - 索引下载（text 包含已完成/全部，实时进度 + 圆环）
    - 切换时后台下载、进度可见、输入与阅读不中断；
  * 分析和理由：语言直接影响“看得懂/是否有解释”，理应常驻但不喧宾夺主。下载是交易性状态，应优先可见但在主任务发生时收敛为环形进度，保障“边下边用”。对网络策略可控，避免意外流量消耗与等待焦虑。

* 决策点：**离线/弱网策略**

  * 结论：离线时可搜索已缓存内容，索引包含单词、音标、coredefinition 单词显示会有变化，下调高度，居中显示。 
  - 顶部弹出 Toast 缓存模式，完整功能需联网访问
  * 分析和理由：先保障“能做的事顺畅可达”，把技术状态转化为可理解的边界说明，不让“网络差”破坏核心任务；禁用与解释并行，避免用户误以为故障。

* 决策点：**系统语言不受支持时的回退**

  * 结论：
  - 应用依赖于learning language 即用户想要学习的语言和scaffolding language 即用户能够看懂的语言。
  - 应用支持的sl语言是有限的
  - 应用需要仅上架到支持语言的国家地区，避免评论出现大量差评拉低产品权重
  - 但当用户在支持国家地区，使用App，但本机语言不在sl支持范围
  - 界面退回基础显示语言，在搜索首页，搜索框上方位置出现文字+可交互按钮说明，查看受支持的讲解语言
  - 点击出现语言设置组件（同索引），允许就地切换到受支持sl并触发下载。
  * 分析和理由：礼貌、可逆、就地解决，比强制弹窗更尊重当下任务；给出明确的“如何修复”的路径，降低挫败感。

* 决策点：**否决本地机器翻译补齐多语**

  * 结论：在质量达不到可用门槛前，不以本地机翻批量生成解释内容；宁可提示未覆盖、引导下载或切回受支持语言。
  * 分析和理由：示例评估显示跨语机器翻译在语义、语域和术语上存在明显偏差，会误导学习与表达；短期“可用”换来的长期“误学”不可接受，宁做清晰边界而非低质妥协。

* 决策点：**视觉策略（微图形 + 数字优先）**

  * 结论：用徽章与线性图标表达等级与锚点，用数字芯片表达“容量证明”；避免大幅插画，Premium 主色仅用于关键 CTA 与徽章。
  * 分析和理由：工具场景讲究效率与可比性，数字与线性图标最易形成“现在就有”的确定感；大图强叙事但弱信息密度，易分散注意力并挤占首屏，得不偿失。

* 决策点：**动效与触感**

  * 结论：状态进出场 150–200ms；首次越过边界给一次轻触感；“付费墙→内容卡”切换必须无闪烁；
  * 分析和理由：动效的任务是“存在感”而非“表演”，统一时长让节奏稳定，触感只在关键阈值出现，帮助建立身体记忆；可访问性设置优先级更高，照顾对动效敏感的用户。

* 决策点：**字体/样式 Token 化**

  * 结论：所有字号、间距、颜色、动效走 Design Tokens；逻辑层不硬编码像素与色值；单行文案建议≤24 个全角字符，对比度≥AA。
  * 分析和理由：Token 化让“先功能后视觉”成为可能：同一逻辑在不同主题与模式下保持一致性；文案长度与对比度设置可自动化校验，减少返工与主观争议。

* 决策点：**无障碍底线与读屏语序**

  * 结论：触达区域 ≥44×44pt；读屏顺序为“名称→值→动作”；所有装饰图形提供适当的可访问标记或被标记为装饰。
  * 分析和理由：移动端可触底线是基本可达性保障；一致的朗读语序降低理解门槛；对装饰元素做正确标记，避免读屏冗余与困扰。

* 决策点：**性能预算与连续性**

  * 结论：首页首屏渲染≤200ms；收敛/展开动效 150–200ms；“付费墙→内容卡”切换保持滚动与焦点连续，无白屏/闪烁。
  * 分析和理由：搜索是高频高期待场景，任何迟滞都会放大；明确预算推动工程做优先级加载与切片渲染；切换连续性是“心流不中断”的关键体感指标。

* 决策点：**遥测与指标闭环**

  * 结论：核心追踪三条：遇墙→升级点击率、遇墙后仍停留/回到搜索比例、次日回访率。
  * 分析和理由：数据闭环才有优化空间；“遇墙后的去向”衡量二分策略是否过刚；“次日回访”观测边界教育是否影响留存。遵循隐私设置，必要时降级采集而不影响功能。

* 决策点：**合规与设置极简**

  * 结论：无注册登录、收藏与配置本地存储；首页或设置中提供“隐私/条款/订阅说明/恢复购买”链接；即便不采集个人数据也不省略披露。
  * 分析和理由：极简降低进入门槛与负担；平台与法律要求仍需满足，合规通路“可达且不打扰”，平衡体验与义务。

* 决策点：**订阅套餐呈现**

  * 结论：提供月度、年度（默认路径）、终生三种；功能权益同权，仅计费节奏不同；改方案在次级轻层完成，不新增第三主按钮。
  * 分析和理由：减少比较维度、降低决策成本；年度以等效月价与节省%提供价格锚点，提高 LTV；将“选择复杂度”收纳到次级交互，主决策保持纯粹。


* 决策点：**工程工作流（先配置后视觉）**

  * 结论：先以配置清单落地区域与状态规则→接数据与交互→自动化校验可读性/无障碍/性能→小流量验证→在 Token 范围内做视觉微调。
  * 分析和理由：把“能用、可测”前置，避免在视觉风格未定时停工；Token 让后期抛光不改逻辑；小流量验证为迭代提供“可撤销”的安全阀。

* 决策点：**状态爆炸防护**

  * 结论：归并状态类型（常态/聚焦/下载中/离线/配额 0/身份变化），每类限制可见项与动画冷却时间；冲突时遵守统一优先级裁剪。
  * 分析和理由：功能增加必然带来状态增殖；分类与上限保证复杂度在可控范围；冷却防止频繁闪烁、保护专注与性能。

* 决策点：**返回搜索的操作统一**

  * 结论：内容卡与付费墙卡上的边缘滑动与左上角返回按钮语义一致，均回到搜索；付费墙卡的“返回搜索”与系统返回效果相同，不引入第三路径。
  * 分析和理由：单一语义避免学习成本与误操作；路径稳定、行为可预期，有助于建立“随时可退”的安全感，降低焦虑与流失。

* 决策点：**搜索页面的元素决策——主输入与快速入口**

  * 结论：保留单一主输入框作为唯一一等入口；右侧仅一枚“快速设置/更改语言”的文字按钮（紧凑态收敛为图标）。输入框首开自动聚焦；提交触发检索；支持回填与再次编辑。
  * 分析和理由：把强意图收敛到一个清晰的起点，避免并列主按钮稀释注意力；“更改语言”与搜索紧密相关，放置在输入框内侧的同一视觉束缚中，解决“去哪里改”的定位问题；紧凑态时收敛为图标，保住输入区可视面积与节奏一致性。自动聚焦降低首次交互摩擦；回填保证“边看边改”的闭环体验。

* 决策点：**搜索页面的元素决策——状态区（上限与排序）**

  * 结论：状态单元最多同时显示三枚；当候选≥4 时，统一按“下载/同步进度 ＞ 当日配额 ＞ 语言/地区 ＞ 身份/会员”取前三。聚焦输入时全部进入紧凑态（图标+极短文案）。
  * 分析和理由：首页的主角是输入与建议，状态数量不加上限会抢占空间并制造跳动；以“是否影响当下可用性”的强弱排序，确保用户总能第一时间看到“此刻最重要”的状态。紧凑态统一节奏，既可见又不打断。

* 决策点：**搜索页面的元素决策——配额胶囊**

  * 结论：常态显示“今日 {left}/{total} · {resetAt} 重置”；聚焦时收敛为“{left}/{total}”。点击弹出轻层解释规则；当 {left}==0 时高亮与可达“升级”。
  * 分析和理由：配额是“可否继续向前”的硬边界，必须清晰、常在、可预期；把重置时间明写，降低焦虑与疑惑；与升级路径原位联通，缩短从感知边界到解决边界的路径。

* 决策点：**搜索页面的元素决策——语言胶囊（界面/内容）**

  * 结论：常态显示“界面：{ui}｜内容：{content}”；点击原位弹出选择器；切换后若需资源，自动触发后台下载（见进度单元）。
  * 分析和理由：语言直接影响可读性与解释质量，应常驻且易改；就地选择避免用户跳设置页迷路；与下载联动，确保“边下边用”。

* 决策点：**搜索页面的元素决策——下载/同步进度**

  * 结论：当存在活跃任务时以进度条/环展示“正在准备 {task} · {percent}%｜仅 Wi-Fi”；提供暂停/继续/取消；聚焦时收敛为环形+百分比。
  * 分析和理由：资源就绪与否决定“能否立刻用”；把进度放到状态优先级第一，减少“为什么还没有”的不确定；交互可控（暂停/仅 Wi-Fi）降低意外流量与等待挫败。

* 决策点：**搜索页面的元素决策——历史与建议**

  * 结论：未聚焦时并列显示“最近×3”和“为你推荐”；聚焦后由建议接管更多行，历史收敛为单行入口；服务异常时回退为“最近×3 + 本地热门”。
  * 分析和理由：历史承接“继续上次”，建议引导“接下来查”；二者并列覆盖熟练与探索两类意图。聚焦后优先建议，减少输入与往返；离线/异常时不阻塞主任务，始终给到可用选项。

* 决策点：**搜索页面的元素决策——辅助与合规**

  * 结论：底部提供“恢复购买/权限”“隐私｜条款｜订阅说明”文字按钮；键盘弹出时隐藏；与主任务无耦合。
  * 分析和理由：合规可达但不打扰；恢复购买在首页即可触达，减少用户求助与退款；键盘弹出时自动让路，保证输入与建议的首要权利。

* 决策点：**搜索页面的元素决策——提示与 Toast**

  * 结论：短时状态以顶部 Toast 呈现（2–3 秒自动消退），如“已切换解释语言”“今日 10/10 用尽 · 明日 0:00 重置”。
  * 分析和理由：边界与确认信息需要“被看到但不被挡住”；Toast 解决“必要即见、自动退场”的矛盾，不打断输入或浏览。

* 决策点：**搜索页面的元素决策——错误与空状态**

  * 结论：建议失败→退为“最近×3 + 本地热门”；无结果→清晰文案 + 可做下一步（改词/更宽泛）；离线→说明“可查缓存/联网解锁”。空状态不占首屏超过 30% 高度。
  * 分析和理由：错误不可避免，关键是“告诉为什么 + 给下一步”；保持主任务连续与可逆，避免“墙一样的大空白”。

* 决策点：**搜索页面的元素决策——可访问性与性能 Token**

  * 结论：触达区域≥44×44；对比度≥AA；单行文案建议≤24 全角；收敛/展开动效 150–200ms；首屏 TTI≤200ms。
  * 分析和理由：这些是“看得见、点得到、不卡”的底线；用 Token 统一这些门槛，保证“先功能后视觉”的同时不牺牲可读性与顺畅度。

---

* 决策点：**付费墙卡片的元素决策——抬头与预览**

  * 结论：抬头固定为“继续探索 · {目标项}”；下方仅一行不可展开预览（灰度/弱对比），不泄露正文。
  * 分析和理由：用户正在“向前”，抬头复述“前方是什么”建立上下文；一行预览满足定位需求但不透支内容，维持“解锁”的价值张力。

* 决策点：**付费墙卡片的元素决策——容量证明（数值芯片区）**

  * 结论：展示 3–4 枚数据芯片，如“例句 {n1}”“搭配 {n2}”“还可探索 {n3}”“离线可用”；数据来源于目标项的元信息（预取）。
  * 分析和理由：用“现在就多看到多少”的量化证据，替代抽象口号；与用户的即时动机强相关，是最直接的付费理由；依赖元信息即可渲染，成本小、反馈快。

* 决策点：**付费墙卡片的元素决策——权益列表（长期能力）**

  * 结论：列出 ≤4 条动宾短句，如“无限跳转”“高级例句与搭配”“离线大词库”“语音朗读（TTS）”；顺序按普适价值从高到低。
  * 分析和理由：容量证明说“当下立刻有”；权益列表说“从此以后都有”。两者合力构建“即刻收益 + 长期承诺”的心智，避免“为恢复被拿走的内容而付费”的负感。

* 决策点：**付费墙卡片的元素决策——主 CTA（升级）**

  * 结论：主按钮为“升级到 Premium”，默认挂载年度方案（文案含价格、等效月价、节省%）；点击后按钮内联进度，成功原位切换为目标内容卡。
  * 分析和理由：减少比较维度，让用户只做“是否升级”的决定；年度默认提高 LTV 且更易通过“等效月价”被理性化；内联进度与原位切换维持心流，不跳层、不白屏。

* 决策点：**付费墙卡片的元素决策——次 CTA（返回搜索）**

  * 结论：“返回搜索”为唯一次按钮；视觉优先级低于主 CTA；点击回首页并回填查询，顶部出现“边界回声”。
  * 分析和理由：给出体面退路，降低被迫感；返回带来温和的“进度损失厌恶”推动转化，但不制造敌意；回填与回声让用户理解“为什么回到搜索”。

* 决策点：**付费墙卡片的元素决策——方案切换（可选轻层）**

  * 结论：在主 CTA 附近提供“更改方案”次级链接，弹出轻层切换“月度/年度/终生”；不新增第三个主按钮。
  * 分析和理由：允许价格敏感用户微调，但不把界面变成价目表；保持“二分主决策”的纯净，减少犹豫与流失。

* 决策点：**付费墙卡片的元素决策——合规与信任文案**

  * 结论：卡片底部以小号文字提供订阅说明（周期、自动续订、取消方式）与“恢复购买”；遵循平台指南（如 App Store 3.1.1）。
  * 分析和理由：交易透明减少退款与审核风险；把说明“降级呈现”，保证不干扰主决策，同时随时可查。

* 决策点：**付费墙卡片的元素决策——错误/离线内联区**

  * 结论：交易失败/网络不可用时，在主 CTA 下方内联展示错误与重试；离线时主 CTA 置灰并提示“需联网完成解锁”，次 CTA 始终可达。
  * 分析和理由：错误不把人踢走；就地告知 + 可逆操作，降低挫败；离线明确边界，避免重复无效点击。

* 决策点：**付费墙卡片的元素决策——图形与品牌**

  * 结论：使用小型徽章/线性图标表达“Premium”，不使用大幅插画；品牌主色仅用于主 CTA 与徽章。
  * 分析和理由：工具型场景讲究信息密度与效率；大插画分散注意、挤占首屏；小徽章传达等级感即可，避免“广告化”。

* 决策点：**付费墙卡片的元素决策——可访问性与动效**

  * 结论：焦点顺序“抬头→容量→权益→主 CTA→次 CTA→合规”；触达区域≥44×44；Reduce Motion 开启时，升级成功切换为不超过 150ms 的淡入。
  * 分析和理由：把“能读、能操作、能撤销”做成一致秩序；对动效敏感用户提供同等路径，不牺牲理解。

* 决策点：**付费墙卡片的元素决策——遥测**

  * 结论：上报曝光、主 CTA 点击、方案切换、购买成功/失败、返回搜索、离线受阻等事件；串联会话 ID 与遇墙来源（第几次跳转）。
  * 分析和理由：转化优化的基础是链路可见；捕捉“第几次遇墙”可用于微调免费配额或文案强度；离线受阻比率用于判断缓存与提示是否需要加强。

如果你希望，我可以把这些“元素决策”再转换成你的前端配置清单（YAML/JSON 键值），对应到每个组件的可见条件、文案 key、埋点事件名与动效 Token，便于直接接入实现与验收。

---

* 决策点：**触发条件与入口统一**

  * 结论：付费墙只在“跨卡浏览”触达边界时触发；默认边界为“当日跨卡配额用尽（`quota.left==0`）且用户上滑至替换阈值”。另外两个受控入口：① 首页配额胶囊点击“了解更多/升级”直达付费墙卡的等价路由；② 功能受限行为（如尝试打开仅订阅可用的离线大词库）走同一付费墙卡，不生成新样式。
  * 分析和理由：统一入口减少学习成本，降低设计/工程分叉。把“遇墙时再出现”放在价值峰值节点，转化更自然。首页与功能受限入口复用同一卡，保证“文案一致、计费一致、埋点一致”，避免统计口径混乱。

* 决策点：**卡片结构与区块层级**

  * 结论：卡片自上而下仅四层：① 抬头（目标项标题 + 一行非展开预览）② 容量证明（数据芯片组，3–4 枚）③ 权益列表（≤4 条动宾短句）④ 行为区（主 CTA：升级；次 CTA：返回搜索；下方极小字号合规与恢复购买）。任何可选信息（活动、折扣）均以“抬头下的细条横幅”承载，**不新增层级**。
  * 分析和理由：四层足以覆盖“定位—证据—承诺—行动”。层级固定使开发可组件化渲染，QA 可按区块验收。横幅处理活动，避免破坏主决策节奏。

* 决策点：**抬头区（定位与不泄露）**

  * 结论：标题固定句式“继续探索 · {target}”；其下一行仅显示“单行预览”，使用弱对比文本，禁止展开/复制。预览来源于预取的目标卡元数据，仅包含首句或标签化摘要，长度≤24 全角，超出截断。
  * 分析和理由：用户刚刚“向前滑”，抬头需重建上下文并确认“我要去哪儿”。单行预览满足“我没走错”的安心，同时不泄露正文，保存“解锁”的张力与价值感。

* 决策点：**容量证明（数据芯片）**

  * 结论：默认展示 3–4 枚芯片，优先顺序：`related_count`→`examples_count`→`collocations_count`→`offline_ready`（或`audio_tts`）。芯片文案统一“名词 + 数值/状态”格式，如“关联·{n3}”“例句·{n1}”“搭配·{n2}”“离线·可用/未安装”。数值来自目标项与周边索引的元信息，进入卡片前预取；获取失败时以“—”显示并退到权益列表强调长期能力。
  * 分析和理由：芯片将“立即可见的增量”量化成可比较的证据，直击“我现在多看到多少”。按“可继续浏览的广度→具体内容深度→用法密度→可用性状态”的顺序排列，贴合即时动机。预取只要元数据，带宽小、响应快。

* 决策点：**权益列表（长期能力）**

  * 结论：最多 4 条、动宾短句，默认顺序：“无限跳转”“高级例句与搭配”“离线大词库”“语音朗读”。文案必须一致、可国际化；若设备离线包已装，自动将“离线大词库”后置并显示“已安装·{size}”，避免重复承诺。
  * 分析和理由：容量证明覆盖“眼前收益”，权益列表承诺“长期收益”；控制数量避免冗长比较，强调“解锁即获得、长期有效”。对已具备的权益降权，避免“承诺已拥有之物”造成不可信。

* 决策点：**定价与方案呈现**

  * 结论：主 CTA 仅挂一个默认方案（年度）；文案包含价格、等效月价与节省比例，如“升级（¥128/年 · ¥10.7/月 · 省 40%）”。旁置“更改方案”轻链接，弹出底部轻层切换“月度/年度/终生”，切换仅影响主 CTA 文案与价格，不新增第三主按钮；所有价格文本源自 StoreKit 产品对象，前端不拼接货币符号、不缓存税额。
  * 分析和理由：年度默认建立强锚点，提高 LTV；把“选择复杂度”下沉到次级轻层，保持主决策纯净。价格须由系统票据提供，避免本地化/税额错误，引发审核与退款风险。

* 决策点：**合规与恢复购买**

  * 结论：行为区下方（视觉优先级最低）显示“订阅说明/自动续订/取消方式”等平台要求文案与“恢复购买”文字按钮；点击“恢复购买”走系统流程，结果内联回写。
  * 分析和理由：交易透明降低纠纷与驳回风险；“可达但不打扰”的放置方式平衡体验与义务。恢复购买是高频售后动作，保持同地可达。

* 决策点：**动效与过渡**

  * 结论：内容卡→付费墙卡采用与“下一张内容卡”相同的推入/覆盖动效，时长 180–200ms；购买成功后“付费墙卡原位替换为目标内容卡”，过渡 ≤150ms 无闪烁，保留滚动位置语境；Reduce Motion 开启时改为≤150ms 的淡变。
  * 分析和理由：一致动效让“遇墙”感觉是“路径被礼貌地拦住”，而非被“踢出场景”；成功后“无缝继续”维持心流。动效降级满足可访问性。

* 决策点：**离线与弱网处理**

  * 结论：若设备离线，主 CTA 置灰并显示“需联网完成解锁”；若网络抖动，CTA 内联旋转进度，失败展示就地错误与“重试”；次 CTA（返回搜索）始终可点击。价格与方案在离线时仍显示上次有效票据，标注“价格可能变动”。
  * 分析和理由：不让技术状态中断主任务：随时可退、错误可识别、操作可逆。离线保留最近票据可减少“空白价”，同时以提示管理预期。

* 决策点：**错误与边缘状态**

  * 结论：覆盖 `pending`（待确认）、`revoked`（撤销/退款）、`expired`（到期）、`grace`（宽限期）、`priceConsentRequired`（价格上调同意）等交易状态：

    * `pending`：在 CTA 下方显示“待确认”，禁用再次购买；
    * `revoked/expired`：降级权益，允许再次购买，显示原因；
    * `grace`：保留权益并提示“宽限期至 {date}”；
    * `priceConsentRequired`：引导前往系统同意页面。
  * 分析和理由：处理交易状态是合规与体验的底盘，内联呈现减少用户迷茫与来回跳转。

* 决策点：**频率控制与再曝光**

  * 结论：在同一会话内，用户从付费墙返回搜索后，默认**不再自动触发**付费墙，直至发生新一次“跨卡上滑”动作；在一天内遇墙 N 次后，对后续遇墙的抬头横幅提示强度渐进但仍保持二分法，不增加“暂时跳过”第三按钮。
  * 分析和理由：减少“连环拦截”的恼怒，同时保持边界教育。强度渐进能在高意向用户身上稍增转化压力，但不破坏交互原则。

* 决策点：**首遇墙与回访用户差异化**

  * 结论：首次遇墙显示“首遇引导”横幅，加入一句“明日 0:00 自动恢复 10 次免费探索”；回访用户（近 7 天内多次遇墙未转化）显示更聚焦的容量证明排序（优先 `related_count` 与 `examples_count`），权益列表精简为 3 条。
  * 分析和理由：首次需要教育规则，回访应减少赘述、强化证明。通过轻微排序策略，让最可能促发点击的证据先入眼。

* 决策点：**A/B 可控变量**

  * 结论：暴露以下可实验变量：免费配额阈值、容量芯片数量与排序、主 CTA 文案强度、年度默认/月度默认、等效月价是否显示、边界横幅是否展示。所有实验旗标“远端配置可缺省，默认安全”，缺省回落到基线方案。
  * 分析和理由：关键扳手前置实验化，便于迭代；默认安全确保远端配置失败不致白屏或逻辑错台。

* 决策点：**文案与国际化**

  * 结论：所有文案使用 i18n key；金额与周期全部通过系统本地化格式化（货币、小数、位置）；禁止硬编码货币符号或税率文案；等效月价文案在不支持的地区（税制差异明显）自动隐藏。
  * 分析和理由：国际化金额呈现是常见审查点；由系统票据驱动的本地化最稳妥，自动规避区分价/含税价差异带来的误导。

* 决策点：**无障碍与阅读顺序**

  * 结论：读屏顺序固定为“抬头→一行预览→容量证明组（从左到右）→权益列表→主 CTA→次 CTA→合规小字→恢复购买”；所有数值芯片提供可访问文本，如“关联可继续 {n3} 条”；触达区域≥44×44pt。
  * 分析和理由：固定顺序让读屏用户获得与视觉用户一致的“先证据后行动”的叙事；数值读法直观，减少理解成本。

* 决策点：**性能与预取**

  * 结论：在用户上滑接近阈值（\~120ms 提前）时预取目标卡**元信息**（标题、预览、计数）；卡片切换首帧≤16ms、可交互≤100ms；容量芯片渲染失败时降级为“—”，不阻塞 CTA。
  * 分析和理由：短预取保证“遇墙卡”即刻可读；把元数据与正文解耦，既保密也提速；降级策略确保“能买”的按钮永远先可用。

* 决策点：**埋点与指标**

  * 结论：最小事件集：`wall_impression`（含来源、target、counts）、`upgrade_click`（含 plan）、`purchase_success/fail`（含错误码）、`return_search`、`offline_blocked`、`plan_switch`、`restore_attempt/success/fail`。核心指标：遇墙→升级点击率、遇墙后仍停留或回到搜索比例、购买成功率、离线受阻率、从遇墙到次日回访率。
  * 分析和理由：事件最小化但覆盖“看见—点击—结果—退路”；指标对应“体验是否打扰”“边界是否有效”“商业是否健康”的三大判断面。

* 决策点：**安全与票据一致性**

  * 结论：价格/方案/试用资格全部由 StoreKit（或等价计费 SDK）实时对象提供；任何远端 AB 仅能调整**展示顺序与文案强度**，不能覆盖价格与周期；收据验证失败时，权益不提升、错误就地提示并允许重试/恢复。
  * 分析和理由：把“钱”的事实交给系统，降低合规与风控风险；将增长实验限定在展示层，避免“价格不一致”引发退款与审查问题。

* 决策点：**已购与终身用户的再进入**

  * 结论：若持有有效订阅或终生购买，**永不再次展示付费墙**；误触发时转为目标内容卡并在底部吐司“已解锁：无限跳转”等简短回声。
  * 分析和理由：尊重已购用户，避免“收过钱还拦”的强烈负体验；同时通过回声建立正向反馈。

* 决策点：**视觉与品牌边界**

  * 结论：不使用大幅插画；Premium 使用小型徽章/线性图标与品牌主色；容量芯片采用高对比、低饱和底，主 CTA 用品牌主色；整体不超过一处强调色块。
  * 分析和理由：工具场景强调“数与字”，减少装饰性噪声；一处主色集中注意力，其他区域以信息密度取胜。

* 决策点：**可配置项清单（供前端提示词/配置使用）**

  * 结论：对外暴露以下可调键：`quota.daily_total`、`wall.preview_max_chars`、`chips.order`、`chips.max_count`、`benefits.list`、`cta.default_plan`、`cta.copy_strength`、`banner.first_wall_enabled`、`ab.flags.*`、`telemetry.session_id`；所有键有**默认值**与**上限/下限**，缺省回落到基线方案。
  * 分析和理由：把“会变的”外露成配置，把“不会变的”固化在组件内，既给增长空间又避免事故外溢；默认与边界值让线上更稳。

如果你愿意，我可以把这份“付费墙信息架构—完整决策”进一步转成你的前端配置清单（YAML/JSON 键值 + 默认值 + 上下限），以及 5 条关键 **BDD/EARS 场景用例**给 QA 直接对条测试（成功购买、离线阻断、状态 pending、再次遇墙频控、恢复购买流）。需要我直接生成那两份工件吗？

---

* 决策点：**设置入口的总体模型（不设专页，融入首页）**

  * 结论：不再单做“设置页”。所有设置以两种形态融合到搜索首页：①“信息即入口”的状态胶囊/文本按钮（可见即可点）；②承载所有二级选项的统一底部弹起 sheet（统一容器，按来源定向到对应分区）。
  * 分析和理由：你的产品强调“强意图=搜索”。把设置外置会打断任务与心流，也会制造“去哪儿改”的寻址成本。将“状态=入口”放在首页既满足“随手可改”，又保留“所见即所改”的可解释性；再以单一 sheet 做二级展开，保证交互和视觉的一致性、减少分叉，降低学习成本与工程维护复杂度。

* 决策点：**主入口形式（搜索框右侧“快速设置”）**

  * 结论：搜索框右侧保留唯一一枚“快速设置/更改语言”文字按钮；聚焦输入时收敛为图标。点击始终打开统一设置 sheet的顶层。
  * 分析和理由：一眼可见的“唯一入口”能形成清晰心智；与搜索框同一视觉束缚中，用户无需离开主任务就能触达设置。紧凑态图标化确保键盘弹出时不挤占输入与建议空间。

* 决策点：**语言相关设置（界面/内容语言）**

  * 结论：首页的“语言胶囊”（界面：{ui}｜内容：{content}）既显示状态又是入口。点击胶囊直达统一 sheet 的“语言分区”，提供：界面语言、内容/解释语言、未覆盖时的回退语言选择，以及必要时的“下载所需资源”。
  * 分析和理由：语言直接影响可读内容与解释质量，属于“高频微调”。用胶囊常驻展示状态最省脑；点击即到 sheet 的定向分区减少跳转；当新语言需资源时，用同一容器完成确认与触发下载，衔接自然。

* 决策点：**网络/下载策略与离线包管理**

  * 结论：当有下载任务或已安装离线包时，首页显示“进度/离线包”胶囊；点击进入统一 sheet 的“下载与存储分区”，包含：仅 Wi-Fi 下载（开关）、暂停/继续/取消、已装语言包列表（容量/版本/更新/移除）、“释放空间”。
  * 分析和理由：下载是“能否马上用”的关键变量，优先级应最高；策略与包管理自然归于同一分区，避免到处找；将“释放空间”与“包列表”放一起，用户在“看到占用”时即可“做决定”，符合行为学的就地性。

* 决策点：**配额呈现与规则说明**

  * 结论：首页“配额胶囊”显示“今日 {left}/{total} · {resetAt} 重置”；点击进入统一 sheet 的“使用与配额分区”，展示：配额规则、重置时间、本机计时说明、遇墙后的选项（升级/返回搜索）说明。
  * 分析和理由：把“数量+规则”放同一个入口，减少误解与客服压力；与付费路径保持语义前后一致，避免遇墙时才首次认知规则。

* 决策点：**会员与订阅管理**

  * 结论：首页“会员徽章”仅提示身份；点击打开订阅计划 sheet（专用购买容器，便于承接 StoreKit/支付流程）。同时，在统一设置 sheet 的“订阅与恢复分区”提供：恢复购买、票据状态、方案切换入口（仅修改文案与价格，不在此处发起购买）。
  * 分析和理由：购买流程天然需要专用容器（权限、校验、失败重试），不宜与通用设置混装；但“恢复/管理”是设置范畴，置于统一 sheet，形成“买在计划、管在设置”的清晰分工。

* 决策点：**数据与隐私（本地优先）**

  * 结论：统一设置 sheet 中提供：iCloud/本地备份开关（默认随系统，用户可关闭备份）、清除本地数据、一键导出日志（去标识化、显式同意）。
  * 分析和理由：你强调“本地不出端”，但仍要给用户“安全擦除/备份控制”的选择权；在设置集中承载这些少见但关键的动作，既不打扰日常，又确保可达与可审计。

* 决策点：**合规与帮助**

  * 结论：统一设置 sheet 底部固定区域提供：隐私、条款、订阅说明、开源许可、联系支持/发送反馈；首页底部仍保留“隐私｜条款｜订阅说明”文字按钮作为冗余直达。
  * 分析和理由：法务链接必须“可达但不打扰”；设置中的固定区域保证一致入口，首页底部提供二次直达以满足平台审查和用户预期。

* 决策点：**统一底部弹起 sheet 的结构与交互**

  * 结论：采用单容器、多分区的上下文感知 sheet：标题区（来源上下文标题+返回锚点）、分区列表（语言；下载与存储；使用与配额；订阅与恢复；数据与隐私；合规与帮助）、操作区（主要/次要动作）。手势支持下拉关闭、上拉扩展至最大高度；深度不超过两级（分区→选项），二级内再需要确认则以轻量确认条或顶部 bar 内联完成，不再开第三级。
  * 分析和理由：单容器降低心智复杂度与技术复杂度；分区化避免长单页滑不完；两级上限防止“设置迷宫”；来源定向（从语言胶囊点进来直接落在语言分区）让“看到—想到—做到”的链条最短；上下拉手势符合移动端惯性与肌肉记忆。

* 决策点：**sheet 的状态与可访问性**

  * 结论：键盘弹出时，sheet 自动避让；VoiceOver/读屏下，焦点顺序为“标题→分区标题→项→主要动作→次要动作→关闭”；动态字体与 Reduce Motion 生效：文案不截断、动效降级为淡入/淡出≤150ms。
  * 分析和理由：设置经常在“输入进行中”被打开（例如切语言），避让与可访问性顺序决定是否“好用”；无障碍与动态字体是学习类产品的刚需，提前作为规范避免后期返工。

* 决策点：**sheet 的文案与国际化**

  * 结论：所有文案用 i18n key；关键名词在各处统一（如“离线大词库”“无限跳转”）；提供伪本地化与 RTL 验证清单。
  * 分析和理由：设置是文案密集区，术语必须一致，避免“同物异名”；伪本地化能尽早发现截断/拼接问题，降低晚期修正成本。

* 决策点：**错误与边缘情形的就地处理**

  * 结论：在 sheet 内对可失败的项（下载、恢复购买）采用内联错误与重试；弱网时禁用相关开关并给予原因说明，不弹全屏对话框。
  * 分析和理由：你强调“不中断主任务”。就地错误让用户看得见、立刻可逆，不被“踢出上下文”；禁用+解释优于允许点击后再报错。

* 决策点：**遥测与实验（设置交互）**

  * 结论：对“打开 sheet→分区曝光→项点击→成功/失败→关闭”的链路埋点；关键实验项：仅 Wi-Fi 默认值、语言分区默认排序（常用/全部）、“释放空间”的呈现位置与文案强度。
  * 分析和理由：设置交互低频但高决策密度，链路数据帮助识别“找不到/看不懂/做不到”的瓶颈；通过实验优化默认值与分区排序，减少首次成本。

* 决策点：**哪些设置不放进统一 sheet**

  * 结论：购买流程仍使用“订阅计划 sheet”（支付专用容器）；遇墙决策仍在“付费墙卡”内完成；统一 sheet 中仅保留“管理/说明/恢复”类入口。
  * 分析和理由：支付涉及系统票据、错误处理与安全提示，复用专用容器可靠且符合平台预期；遇墙是“任务流内的决策点”，放在内容流里才不割裂。统一 sheet 聚焦“长期设置与说明”，职责单一、边界清晰。

* 决策点：**元素到 sheet 的映射（首页 → 设置）**

  * 结论：

    * “快速设置”按钮 → 打开 sheet 顶层；
    * 语言胶囊 → sheet·语言分区；
    * 进度/离线包胶囊 → sheet·下载与存储分区；
    * 配额胶囊 → sheet·使用与配额分区；
    * 会员徽章 → 订阅计划 sheet（购买）；
    * 首页“恢复购买/合规”文字按钮 → 直接打开对应页面/分区。
  * 分析和理由：一对一映射降低寻址时间；用户看到哪个状态，就点哪个状态进入对应控制台；购买与管理的分离避免“混用容器”带来的流程复杂与审查风险。

* 决策点：**工程实现与回退策略**

  * 结论：统一 sheet 作为单组件，分区通过配置驱动；远端配置失败时，默认只展示“语言”“下载与存储”“订阅与恢复”“合规与帮助”四个基础分区；其余隐藏不影响主任务。
  * 分析和理由：设置容器一处实现、处处复用，减少技术债；“配置可选，默认安全”确保线上稳定性；隐藏非关键分区避免“半拉子 UI”。

## 套餐定价 

* **决策点：定价框架与市场定位**

  * **结论**：采用“月度 + 年度（默认）+ 终生”的三层梯度；把产品定位为“词典级检索永久免费 + 关联探索为核心权益”的**工具型订阅**，不与课程类订阅（重陪伴/重课时）正面对标。
  * **分析和理由**：你的价值曲线在“即时查阅 + 知识网络心流”，而非班课/打卡陪练。用三层梯度可覆盖试水、稳定付费与强厌订阅人群。若与课程价位并轨（\$10+/月），会引发价值错位与转化困难；若定到超低（月\$1–2），则缺乏“为权益买单”的信号与可持续性。三层结构既建立清晰锚点，又便于做 A/B 与分层拉升 LTV。

* **决策点：月度订阅定价**

  * **结论**：官方基准 **\$3.99/月**；实验带宽 **\$2.99 ↔ \$4.99**。
  * **分析和理由**：月付承担“低 fricition 试水”角色，定价过高逼近课程类、过低则蚕食年付目标。\$3.99 在工具型心智下具备“可尝试、可理解”的弹性，也为冲动期/旺季提供入口。设置带宽是为了早期以小流量检测价格弹性与“遇墙→升级点击率”。

* **决策点：年度订阅定价（默认 CTA）**

  * **结论**：官方基准 **\$24.99/年**（等效 **\$2.08/月**）；实验带宽 **\$19.99 ↔ \$29.99**；在付费墙主按钮默认展示“年度方案 + 等效月价 + 节省%”。
  * **分析和理由**：年付是营收与留存的主引擎。对比课程类的 \$60–\$80/年，\$24.99 形成显著理性差价，又明显高于“一次性纯工具 \$25”的单点购买，维持订阅模型的可持续。等效月价与节省比例是年付转化的关键行为线索，需同屏呈现以降低计算负担。

* **决策点：终生买断定价**

  * **结论**：官方基准 **\$59.99 终生**；实验带宽 **\$49.99 ↔ \$79.99**；在“更改方案”轻层中呈现，不作为主按钮。
  * **分析和理由**：买断面向“强厌订阅/重度词典用户”。价位需显著高于年付以避免大规模蚕食订阅，又远低于课程类“终生 \$150+”以符合工具心智。置于次级层可降低“以价择算”的分散效应，保持付费墙“二分闭环”的简洁。

* **决策点：价格呈现与交互**

  * **结论**：主 CTA 固定单一默认方案（年度）；旁置“更改方案”轻链接（切换月度/年度/终生仅影响主 CTA 文案与价格，不新增第三主按钮）。
  * **分析和理由**：把“是否升级”的主决策与“选哪种”次决策解耦，减少犹豫点与跳出；维持“原位二选一（升级/返回）”的纯净结构，最大化心流续接。

* **决策点：地域与税费合规（票据驱动）**

  * **结论**：价格、货币符号、税费/试用资格一律以 StoreKit/官方计费 SDK 的实时产品对象为准；前端不硬编码金额与货币；等效月价在税制差异显著地区可按规则自动隐藏。
  * **分析和理由**：金额本地化与税制合规是高风险区。让票据成为**唯一事实来源**可避免审核驳回与退款争议；对等效月价的条件隐藏，避免“含税/未税”的误导叙事。

* **决策点：A/B 实验与价格带宽治理**

  * **结论**：首发期以小流量并行试验：年付 **\$19.99/\$24.99/\$29.99**；月付 **\$2.99/\$3.99/\$4.99**；终生 **\$49.99/\$59.99/\$79.99**。关键指标：遇墙→升级点击率、购买成功率、D+1 回访率、退款率；异常（转化骤降或退款异常）时自动回落到基线。
  * **分析和理由**：你的转化节点集中在“遇墙当下”，需在同一场景下验证价格弹性。用小流量先跑“价格 × 文案强度”矩阵，凭数据做锚点微调；配合自动回落防止线上事故放大。

* **决策点：老用户与调价（祖父条款）**

  * **结论**：涨价不影响已订阅用户的当前周期；到期后以平台“价格同意”流程为准；老用户“祖父价”策略在后端可配置并清晰告知。
  * **分析和理由**：价格稳定性是订阅信任的根；尊重祖父价可降低流失与差评风险；清晰沟通减少客服压力与负面口碑。

* **决策点：多语种扩张的产品形态**

  * **结论**：**坚持单 App 架构**；多语种作为**同一订阅权益**的一部分（“全部语种可用”），**不**为每个语种/母语单独开新 App，也**不**以“新增语种”为唯一理由对现有订阅统一涨价。
  * **分析和理由**：多 App 会稀释评分与搜索排名、加倍 ASO 与投放成本、割裂用户口碑与留存；学习路径常跨语种，单 App 更能发挥“知识网络”的网络效应；商店对“壳相同、内容分割”的上架并不友好。把“全语种可用”写进权益清单，可显著提升年付的长期价值叙事。


* **决策点：新语种上线策略与门槛**

  * **结论**：新语种先在订阅内“软上线”（在线可用、基础离线就绪），监测使用率与边际成本，再决定是否推出“高级离线包”附加内购；上线门槛包括：覆盖率、例句质量、TTS/音频可用性、版权合规与体积预算。
  * **分析和理由**：先把“能用”交付，再逐步加料，避免一次性重投入；用真实使用与成本数据决定是否推高级包，降低错误投资与沉没成本。

* **决策点：不开新 App 的商业与体验理由**

  * **结论**：不拆包、不多壳；所有语种统一票据与合规；ASO 聚合在单应用下以积累口碑与排名。
  * **分析和理由**：口碑与排名的复利来自**单一入口**；多包会让评价与下载分流、运营成本倍增；统一票据免去“跨包迁移/合并”的复杂后果，降低技术债。

* **决策点：付费墙中的语种权益表达**

  * **结论**：在“权益列表”里长期固定一条“**全部语种可用**”，并在“容量证明”区按目标项显示“可继续关联 {n}（含跨语种）”；若设备已安装某语种离线包，则将“离线大词库”权益后置并标注“已安装·{size}”。
  * **分析和理由**：把多语优势显性化，直接提升年付的感知价值；与芯片数据联动，形成“立刻可见的广度 + 长期可用的深度”的互证；对已拥有权益降权，避免承诺冗余带来的不信任。

* **决策点：指标与监测（与定价/语种策略联动）**

  * **结论**：核心监测三类：① 商业——遇墙→升级点击率、购买成功率、退款率、ARPU、年付占比；② 体验——D+1/D+7 回访、遇墙后的去向（继续/返回搜索/退出）；③ 语种——新语种活跃度、离线包渗透率、离线命中率与带宽成本。异常阈值触发自动回退（如价格回落到基线、隐藏等效月价、暂停高级包推广）。
  * **分析和理由**：没有量化就没有优化。将商业、体验与语种成本放在同一看板，才能做出“降价换量”“提价稳收”“扩语种还是加深单语”的理性选择；自动回退为线上保驾。

* **决策点：用户沟通与祖父价文案**

  * **结论**：调价或新增语种时，面向存量订阅者用“祖父价不变/到期提示”的轻通知；在付费墙与设置页同步更新文案，确保“新权益已包含在订阅里”的正向叙事。
  * **分析和理由**：价格与权益变更最易引发不满。以“既得利益不被削减”的口径沟通，可减少流失与舆情；在关键界面同步文案，避免“说法不一致”引起投诉。
