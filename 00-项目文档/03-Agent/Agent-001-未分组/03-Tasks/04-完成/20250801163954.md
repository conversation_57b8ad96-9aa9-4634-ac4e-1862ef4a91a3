# TODO 
即使你没有积压的任务，但是只要你需要阅读和理解代码，这本来就是一个会和实际开发过程产生冲突的任务，在 augment 编码的时候你就必须等待或者浪费时间去听音乐，
而因为需要处理合并冲突，所以目前来看最少需要三个分支，一个是 Plan（负责） 一个是 Dev（负责的是分支Merge和潜在的冲突处理），一个是具体的功能特性分支。

1. 我需要创建支持 worktree 并行开发的结构
2. 显示当前 git 的所有分支，目前只有两个分支，一个是 dev 当前分支，一个是 main 分支
3. 我需要提交，然后我需要创建 Plan 工作区

使用 git worktree特性支持并行特性开发
	1. 建议的特性分支名称: `feature/auth/email-registration-login` 
	2. 建议的 git worktree 文件路径：/path/to/your/new/worktree-directory（请参考根目录同层工作区，为用户推荐文件命名，不使用该模板占位内容）
	3. 当前分支情况，Plan（负责代码审阅和计划创建）、Dev（分支Merge和潜在的冲突处理），其余是具体的功能特性分支。
	4. 功能特性分支会从 dev 拉取计划
	5. 为用户提供分支创建模拟命令行语句

```bash
# 概念性命令，用于记录和指导
git checkout dev
git pull origin dev
git worktree add /path/to/your/new/worktree-directory -b feature/auth/email-registration-login dev
```
