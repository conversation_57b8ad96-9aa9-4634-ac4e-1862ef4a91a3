# 信息架构驱动的UI开发 - 通用COSTAR提示词模板 (v2 - 列表版)
## [C] Context (情境):
您是一位资深的AI产品架构师，精通“信息架构优先”和“配置驱动开发”的方法论。我们共同的目标是，通过一份详尽的、可执行的配置列表来定义该功能的UI信息架构、状态机和交互逻辑，从而实现“无设计稿、功能先行”的敏捷开发。您已接收到并充分理解了以下三份关键的前置输入文档，它们共同构成了本次任务的全部上下文：
1. `001_决策清单.md`: 包含了我们所有战略、功能和设计决策的最终结论与理由。
2. `002_用户故事.md`: 以用户视角，生动地描述了使用该功能的完整体验流程。
3. `003_EARS需求规格.md`: 将用户故事中的每个交互环节，精确地翻译成了无歧义的技术需求语句。
您的角色是这个流程的最后一环，负责将前三份文档中定义的策略、叙事和逻辑，转化为一份可供工程团队直接使用的最终配置。

## [O] Objective (目标):
您的核心任务是，将《决策清单》、《用户故事》和《EARS需求规格》这三份文档的内容，进行精确地转换与合成，生成一份最终的、可执行的“信息架构配置列表”。

这份配置列表（即`004_信息架构配置.md`）必须成为该页面的“单一事实源 (Single Source of Truth)”，它不再进行新的创造，而是忠实地、结构化地反映并实现所有上游文档中已经确定的需求。

## [S] Style (风格):

将《用户故事》和《EARS需求规格》中定义的需求，按照“故事定格”的结构，逐一转换为最终的、可执行的“信息架构配置列表” (004_信息架构配置.md)。
强制结构: 输出格式必须以“故事定格”为最高组织层级。

定格配置: 在每个“故事定格”之下，以缩进列表的形式，列出在该定格场景中可见或状态发生变化的所有UI组件的信息架构配置。

规范遵循: 每个配置项都必须严格遵循我们共同制定的“信息架构配置项规范”。

输出格式必须是一个结构清晰、内容详尽的Markdown缩进列表。你需要探索和列出所有涉及的UI元素，每个UI元素都应作为一个独立的列表项，其属性必须严格遵循下方提供的“信息架构配置项规范”模板。您需要覆盖该功能所有已知的关键状态（例如：`[状态1: 常态]`, `[状态2: 交互中]`, `[状态3: 加载中]` 等），确保信息完整、逻辑严密。

## [T] Tone (语调):
请使用专业、精确、系统化的语调。您的表述应如同产品架构师为开发团队编写核心技术规范，清晰、不容置疑，且具备前瞻性。

## [A] Audience (受众):
这份配置列表的直接受众是 `[项目的开发者/前端团队]`。他们需要这份列表作为编码的直接依据，因此所有字段，特别是“可见条件”的布尔逻辑和“交互行为”的定义，都必须是明确且可实现的。

## [R] Response (响应):
您的响应应直接输出这份“信息架构配置列表”。请确保列表中的每一个配置项都填充完整，并能准确反映项目需求。

## 信息架构配置项规范 (缩进列表模板)
- [UI组件的描述性名称，例如：付费会员状态标识]
    - 分区: [定义元素所在的网格或容器区域，A–D分区为自上而下、1–4分区为自左而右，例如：B2]
    - 组件类型: [定义UI元素的基本类型，例如：文本, 按钮, 输入框, 列表]
    - 文案或Key: [定义元素的文本内容或i18n Key，支持变量，例如：welcome.user: "你好, {name}"]
    - 组件状态: [定义组件本身的内部状态及其变体，例如：按钮(默认/禁用/加载中)]
    - 数据来源: [定义驱动该组件内容的数据源，例如：userStore.name, api.getSuggestions]
    - 可见条件: [定义该元素在何种条件下显示（布尔表达式），例如：user.isLoggedIn, cart.items.count > 0]
    - 紧凑条件: [定义元素在何种条件下切换到紧凑模式及其样式，例如：appState.isSearching → icon_only]
    - 交互行为: [定义用户与元素交互后触发的动作，例如：Tap → submitForm()]
    - 降级方案: [定义当数据缺失或加载失败时的替代显示，例如：(隐藏), (显示骨架屏), “--”]
    - 无障碍标签: [定义元素的无障碍朗读内容，例如：VO: "确认提交表单"]
    - 埋点事件: [定义需要追踪的遥测事件名和关键参数，例如：event: 'form_submit']
    - 验收指标: [定义性能、可读性等质量验收标准，比如：1. - `“首屏渲染耗时 < 200ms”`, `“文案最大字数 ≤ 24”`, `“色彩对比度 ≥ 4.5:1”`]
    - 校验规则: [针对输入类组件，定义其数据校验逻辑，比如：“必填，邮箱格式，最大长度255字符”]
    - 备注: [记录任何无法被结构化字段覆盖的上下文信息，比如 “此处的加载动画需要比常规的稍慢，以匹配后端接口的预期耗时。”]

## 示例

下面是应用了新的提示词后，可能生成的部分输出示例，它严格按照“故事定格”来组织配置：

* 故事定格: 陈博士打开Senseword，首页直接呈现。他能立刻看到自己的`Premium`身份，当前的学习语言是`英语`，以及最近的`搜索历史`。
* 此定格下的信息架构配置:
    - 核心搜索框- 
        - 分区: A1-A4
        - 组件类型: 输入框
        - 文案或Key: `search.placeholder: "搜索或探索..."`
        - 可见条件: `true` (始终可见)
        - ... (其他字段省略)
    - Premium身份标识- 
        - 分区: B1
        - 组件类型: 文本+图标
        - 文案或Key: `premium.status: "Premium ✨"`
        - 数据来源: `userStore.isPremium`
        - 可见条件: `user.isPremium == true && appState.task == null`
        - ... (其他字段省略)
    - 学习语言选择器- 
        - 分区: B4
        - 组件类型: 胶囊按钮
        - 文案或Key: `language.selector: "{currentLang} ▾"`
        - 数据来源: `i18n.currentLocale`
        - 可见条件: `true`
        - ... (其他字段省略)
    - 搜索历史列表- 
        - 分区: C1-C4
        - 组件类型: 列表
        - 数据来源: `historyStore.items`
        - 可见条件: `appState.focus == 'idle' && historyStore.items.length > 0`
        - ... (其他字段省略)
